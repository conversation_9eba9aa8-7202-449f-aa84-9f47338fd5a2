<template>
	<div class="digital-human-transition-app">
		<!-- 顶部导航条区域 -->
		<Headbar />

		<!-- 主要内容区域 -->
		<div class="main-content">
			<!-- 顶部功能卡片区域 -->
			<div class="feature-cards-section">
				<div class="feature-card" @click="navigateToDigitalHuman">
					<img src="@/assets/img/changjian1.png" alt="创建数字人作品" />
				</div>
				
				<div class="feature-card">
					<img @click="jumpPage('/videoDigitalHuman')" src="@/assets/img/changjian2.png" alt="定制数字人" />
				</div>
			</div>

			<!-- 我的作品区域 -->
			<div class="works-section">
				<div class="section-header">
					<h2>我的作品</h2>
					<a href="#" class="view-more" @click.prevent="navigateToMyWorksDetail">
						查看更多
						<img src="@/assets/img/you1.png" alt="查看更多" class="view-more-icon" />
					</a>
				</div>
				<div class="works-container">
					<DigitalHumanWorks :selectedFileId="null" :workTab="workTab" :pageSize="6" :enableInfiniteScroll="false" :hideCue="true" :showTextButtons="true" />
				</div>
			</div>

			<!-- 我的数字人区域 -->
			<div class="digital-humans-section">
				<div class="section-header">
					<h2>我的数字人</h2>
					<a href="#" class="view-more" @click.prevent="navigateToMyDigitalHumansDetail">
						查看更多
						<img src="@/assets/img/you1.png" alt="查看更多" class="view-more-icon" />
					</a>
				</div>

				<!-- 加载状态 -->
				<div v-if="myDigitalHumansLoading" class="loading-container">
					<div class="loading-text">正在加载我的数字人...</div>
				</div>

				<!-- 错误状态 -->
				<div v-else-if="myDigitalHumansError" class="error-container">
					<div class="error-text">{{ myDigitalHumansError }}</div>
					<button @click="getMyDigitalHumansList" class="retry-button">重试</button>
				</div>

				<!-- 数字人列表 -->
				<div v-else class="humans-grid">
					<div class="human-item" v-for="(item, index) in digitalHumansProgress" :key="item.id">
						<div class="human-avatar" @mouseenter="showDigitalHumansCreateVideo(index)" @mouseleave="hideDigitalHumansCreateVideo(index)">
							<img
								:src="item.picUrl || '@/assets/img/ceshi1.png'"
								alt="数字人头像"
								@error="handleImageError($event, index)"
							/>
							<div class="create-video-overlay" :class="{ show: digitalHumansHoveredIndex === index && !dropdownVisible && item.status === 'normal' }">创建视频</div>
							<!-- 生成中状态 -->
							<div class="generating-overlay" v-if="item.status === 'generating'">
								<img src="@/assets/img/dongtai1.gif" alt="生成中" />
								<p>生成中...{{ item.progress }}%</p>
							</div>
							<!-- 失败状态 -->
							<div class="failed-overlay" v-if="item.status === 'failed'">
								<!-- 删除图标 -->
								<img src="@/assets/img/delete.png" @click="deleteDigitalHuman(index)" class="delete-icon" />
								<!-- 失败标签 -->
								<div class="failure-label">失败</div>
							</div>
							<!-- 三个点菜单 -->
							<el-dropdown
								trigger="hover"
								placement="bottom"
								@command="handleCommand"
								@visible-change="handleDropdownVisibleChange"
								v-if="item.status !== 'generating' && item.status !== 'failed'"
								class="three-dots-dropdown">
								<div class="three-dots">⋮</div>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item :command="`rename-${index}`">重命名</el-dropdown-item>
										<el-dropdown-item :command="`delete-${index}`">删除</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</div>
						<div class="human-name">
							<!-- 编辑状态显示输入框 -->
							<div v-if="editingIndex === index" class="edit-name-container">
								<input 
									v-model="editingName" 
									class="edit-name-input" 
									@keyup.enter="saveRename(index)"
									@keyup.esc="cancelRename"
									@blur="saveRename(index)"
									ref="editInput"
									autofocus
								/>
							</div>
							<!-- 正常状态显示名字 -->
							<span v-else>{{ item.name }}</span>
						</div>
					</div>
				</div>
			</div>

			<!-- 公共数字人区域 -->
			<div class="public-humans-section">
				<div class="section-header">
					<h2>公共数字人</h2>
					<a href="#" class="view-more" @click.prevent="navigateToPublicDigitalHumansDetail">
						查看更多
						<img src="@/assets/img/you1.png" alt="查看更多" class="view-more-icon" />
					</a>
				</div>
				<div class="humans-grid">
					<div class="human-item" v-for="(item, index) in publicDigitalHumansList.slice(0, 7)" :key="item.id">
						<div class="public-avatar" @mouseenter="showPublicHumansCreateVideo(index)" @mouseleave="hidePublicHumansCreateVideo(index)">
							<!-- 使用从API获取的数字人图片 -->
							<template v-for="(figure, figureIndex) in item.figures" :key="figureIndex">
								<img v-if="figure.type != 'circle_view'" :src="figure.cover" alt="公共数字人头像" />
							</template>
							<!-- 创建视频按钮 -->
							<div class="create-video-overlay" :class="{ show: publicHumansHoveredIndex === index }" @click="navigateToDigitalHumanEditor(item)">
								创建视频
							</div>
						</div>
						<div class="human-name">{{ item.name }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
// 导入统一的Headbar组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
// 导入数字人作品组件
import DigitalHumanWorks from '@/views/modules/mySpace/myWorks/components/DigitalHumanWorks.vue'
// 导入数字人相关API
import { getDigitalHumanList } from '@/api/digitalHumanLeftOperate.js'
import { getDigitalHumanListByUserId, batchDeleteDigitalHuman } from '@/api/digitalHuman.js'
import { updateWorksStatus } from '@/api/mySpace.js'
import { useloginStore } from '@/stores/login'

// 初始化router
const router = useRouter()

// 获取用户ID
const loginStore = useloginStore()
const getUserId = () => {
    return loginStore.userId || ''
}

// 工作标签状态
const workTab = ref('digital')

// 鼠标悬停状态
const digitalHumansHoveredIndex = ref(null)
const publicHumansHoveredIndex = ref(null)

// 三个点菜单状态
const digitalHumansMenuIndex = ref(null)

// 编辑状态
const editingIndex = ref(null)
const editingName = ref('')

// 下拉菜单状态
const dropdownVisible = ref(false)

// 我的数字人数据加载状态
const myDigitalHumansLoading = ref(false)
const myDigitalHumansError = ref(null)

// 生成中状态更新定时器
const myDigitalHumansTimer = ref(null)

// 数字人生成进度数据
const digitalHumansProgress = ref([
	{ id: 1, progress: 0, status: 'normal', name: '正常数字人', picUrl: '' },
	{ id: 2, progress: 0, status: 'failed', name: '失败数字人', picUrl: '' }, // 临时测试：失败状态
	{ id: 3, progress: 50, status: 'generating', name: '生成中数字人1', picUrl: '' }, // 示例：第3个数字人正在生成中，进度50%
	{ id: 4, progress: 25, status: 'generating', name: '生成中数字人2', picUrl: '' }, // 新增：第4个数字人正在生成中，进度25%
	{ id: 5, progress: 75, status: 'generating', name: '生成中数字人3', picUrl: '' }, // 新增：第5个数字人正在生成中，进度75%
	{ id: 6, progress: 0, status: 'normal', name: '正常数字人', picUrl: '' },
	{ id: 7, progress: 0, status: 'normal', name: '正常数字人', picUrl: '' }
])

// 公共数字人列表数据
const publicDigitalHumansList = ref([])
const publicDigitalCurrentPage = ref(1) // 公共数字人分页
const publicDigitalTotalPage = ref('')
const publicNoMoreData = ref(false) // 公共数字人数据是否加载完毕

// 公共数字人生成进度数据
const publicHumansProgress = ref([
	{ id: 1, progress: 0, status: 'normal' },
	{ id: 2, progress: 0, status: 'normal' },
	{ id: 3, progress: 75, status: 'generating' }, // 示例：第3个公共数字人正在生成中，进度75%
	{ id: 4, progress: 0, status: 'normal' },
	{ id: 5, progress: 0, status: 'normal' },
	{ id: 6, progress: 0, status: 'normal' },
	{ id: 7, progress: 0, status: 'normal' }
])

// 监听进度变化，当达到100%时自动恢复状态
const checkProgressCompletion = (progressList) => {
	progressList.forEach(item => {
		if (item.status === 'generating' && item.progress >= 100) {
			item.status = 'normal'
			item.progress = 0
			console.log(`数字人 ${item.id} 生成完成`)
		}
	})
}

// 更新数字人生成进度
const updateDigitalHumansProgress = (newProgress) => {
	digitalHumansProgress.value = newProgress
	checkProgressCompletion(digitalHumansProgress.value)
}

// 更新公共数字人生成进度
const updatePublicHumansProgress = (newProgress) => {
	publicHumansProgress.value = newProgress
	checkProgressCompletion(publicHumansProgress.value)
}

// 显示我的数字人创建视频按钮
const showDigitalHumansCreateVideo = (index) => {
	// 如果下拉菜单正在显示，则不显示创建视频按钮
	if (dropdownVisible.value) {
		return
	}

	// 检查数字人状态，只有正常状态才显示创建视频按钮
	const digitalHuman = digitalHumansProgress.value[index]
	if (!digitalHuman || digitalHuman.status !== 'normal') {
		return // 失败状态、生成中状态或其他非正常状态不显示按钮
	}

	digitalHumansHoveredIndex.value = index
}

// 隐藏我的数字人创建视频按钮
const hideDigitalHumansCreateVideo = (index) => {
	digitalHumansHoveredIndex.value = null
}

// 显示公共数字人创建视频按钮
const showPublicHumansCreateVideo = (index) => {
	publicHumansHoveredIndex.value = index
}

// 隐藏公共数字人创建视频按钮
const hidePublicHumansCreateVideo = (index) => {
	publicHumansHoveredIndex.value = null
}

// 处理下拉菜单命令
const handleCommand = (command) => {
	const [action, index] = command.split('-')
	const indexNum = parseInt(index)
	
	if (action === 'rename') {
		renameDigitalHuman(indexNum)
	} else if (action === 'delete') {
		deleteDigitalHuman(indexNum)
	}
}

// 处理下拉菜单显示状态变化
const handleDropdownVisibleChange = (visible) => {
	dropdownVisible.value = visible
	if (visible) {
		// 当下拉菜单显示时，隐藏创建视频按钮
		digitalHumansHoveredIndex.value = null
	}
}

// 重命名数字人
const renameDigitalHuman = (index) => {
	console.log(`重命名数字人 ${index + 1}`)
	editingIndex.value = index
	editingName.value = digitalHumansProgress.value[index].name // 设置为当前名字
}

// 保存重命名
const saveRename = (index) => {
	console.log(`保存重命名: ${editingName.value}`)
	// 更新数据中的名字
	digitalHumansProgress.value[index].name = editingName.value
	// 这里可以调用API保存新名字
	editingIndex.value = null
	editingName.value = ''
}

// 取消重命名
const cancelRename = () => {
	editingIndex.value = null
	editingName.value = ''
}

// 删除数字人
const deleteDigitalHuman = async (index) => {
	try {
		const digitalHuman = digitalHumansProgress.value[index]
		if (!digitalHuman || !digitalHuman.id) {
			console.error('数字人数据不存在或缺少ID')
			return
		}

		console.log(`开始删除数字人: ${digitalHuman.name} (ID: ${digitalHuman.id}, 类型: ${typeof digitalHuman.id})`)

		// 确保ID被包装在数组中，无论ID是字符串还是数字
		const deleteParams = {
			ids: [digitalHuman.id]
		}

		console.log('删除参数:', deleteParams)
		console.log('参数中的ids:', deleteParams.ids)
		console.log('ids是否为数组:', Array.isArray(deleteParams.ids))
		console.log('数组长度:', deleteParams.ids.length)
		console.log('第一个元素:', deleteParams.ids[0], '类型:', typeof deleteParams.ids[0])

		// 调用批量删除接口，传入单个ID的数组
		const response = await batchDeleteDigitalHuman(deleteParams)

		console.log('删除数字人成功:', response)

		// 删除成功后，从本地数据中移除该项
		digitalHumansProgress.value.splice(index, 1)

		// 可选：显示成功提示
		// ElMessage.success('删除成功')

	} catch (error) {
		console.error('删除数字人失败:', error)
		// 可选：显示错误提示
		// ElMessage.error('删除失败，请重试')
	}
}

// 注意：重新生成数字人功能已移除，失败状态只保留删除功能

// 处理图片加载错误
const handleImageError = (event, index) => {
	console.log(`数字人 ${index + 1} 图片加载失败，使用默认图片`)
	// 图片加载失败时，使用默认图片
	event.target.src = require('@/assets/img/ceshi1.png')
}

// 映射数字人状态：将API返回的数字状态映射为组件期望的字符串状态
const mapDigitalHumanStatus = (apiStatus) => {
	// 与"我的作品"模块保持一致的状态映射
	switch (String(apiStatus)) {
		case '0':
			return 'generating' // 生成中
		case '1':
			return 'normal'     // 成功/正常
		case '2':
			return 'failed'     // 失败
		default:
			console.warn('未知的数字人状态:', apiStatus)
			return 'normal'     // 默认为正常状态
	}
}

// 导航到数字人作品页面
const navigateToDigitalHuman = () => {
	// 记录来源页面，用于返回时使用
	const fromPage = router.currentRoute.value.path
	router.push({
		path: '/digital-human-editor-page',
		query: { from: fromPage }
	})
}

// 导航到我的作品详情页面
const navigateToMyWorksDetail = () => {
	router.push({
		path: '/my-works-detail'
	})
}

// 导航到我的数字人详情页面
const navigateToMyDigitalHumansDetail = () => {
	router.push({
		path: '/my-digital-humans-detail'
	})
}

// 导航到公共数字人详情页面
const navigateToPublicDigitalHumansDetail = () => {
	router.push({
		path: '/public-digital-humans-detail'
	})
}

// 跳转到数字人编辑器页面（复用PublicDigitalHumansDetail.vue的逻辑）
const navigateToDigitalHumanEditor = (item) => {
    const fromPage = router.currentRoute.value.path

    // 获取数字人的图片URL
    let digitalHumanUrl = ''
    let figuresType = ''
    if (item.figures && item.figures.length > 0) {
        // 查找非circle_view类型的图片
        const imageItem = item.figures.find(figure => figure.type !== 'circle_view')
        if (imageItem && imageItem.cover) {
            digitalHumanUrl = imageItem.cover
            figuresType = imageItem.type
        }
    }

    // 将数字人数据编码为JSON字符串传递
    const digitalHumanData = {
        id: item.id,
        name: item.name,
        figures: item.figures,
        url: digitalHumanUrl,
        figuresType: figuresType
    }

    console.log('🎭 从中转页面跳转到数字人编辑器:', digitalHumanData)

    router.push({
        path: '/digital-human-editor-page',
        query: {
            from: fromPage,
            digitalHumanId: item.id,
            digitalHumanName: item.name,
            digitalHumanData: JSON.stringify(digitalHumanData)
        }
    })
}

// 页面挂载时的初始化逻辑
onMounted(() => {
	console.log('数字人中转页面已加载')

	// 获取我的数字人列表
	getMyDigitalHumansList()

	// 获取公共数字人列表
	getPublicDigitalHumansList(publicDigitalCurrentPage.value, 100)
	// 这里可以调用接口获取实际的生成进度
	// getDigitalHumansProgress()

	// 启用模拟进度更新（用于演示，实际使用时请注释掉）
	simulateProgressUpdate()
})

// 获取数字人生成进度的接口函数（示例）
// const getDigitalHumansProgress = async () => {
//     try {
//         const response = await fetch('/api/digital-humans/progress')
//         const data = await response.json()
//         updateDigitalHumansProgress(data)
//     } catch (error) {
//         console.error('获取数字人生成进度失败:', error)
//     }
// }

// 获取我的数字人列表数据
const getMyDigitalHumansList = async () => {
    try {
        myDigitalHumansLoading.value = true
        myDigitalHumansError.value = null

        const userId = getUserId()
        if (!userId) {
            throw new Error('用户ID不存在')
        }

        console.log('开始获取我的数字人列表，用户ID:', userId)

        // 调用接口获取数字人列表，设置默认显示7条
        const response = await getDigitalHumanListByUserId({
            page: 1,
            size: 7,
            userId: userId
        })

        console.log('获取我的数字人列表API响应:', response)

        // 处理API返回的数据
        if (response && response.records ) {
            const apiList = response.records
 
            // 将API数据转换为组件期望的格式
            const convertedList = apiList.map((item, index) => {
                const mappedStatus = mapDigitalHumanStatus(item.status)

                // 临时测试：强制第一个数字人为失败状态，第二个数字人为生成中状态（用于UI测试）
                let testStatus = mappedStatus
                if (index === 0) {
                    testStatus = 'failed'  // 第一个：失败状态
                } else if (index === 1) {
                    testStatus = 'generating'  // 第二个：生成中状态
                }

                return {
                    id: item.id || (index + 1),
                    progress: testStatus === 'generating' ? (item.progress || 60) : 0, // 生成中时使用API进度或默认60%，否则为0
                    status: testStatus, // 临时使用测试状态
                    name: item.name || `数字人${index + 1}`, // 使用API返回的名称或默认名称
                    picUrl: item.picUrl || '', // 数字人头像URL
                    originalData: item // 保存原始数据以备后用
                }
            })

            // 确保只显示7条数据
            digitalHumansProgress.value = convertedList.slice(0, 7)

            console.log('我的数字人列表数据转换完成:', digitalHumansProgress.value)

            // 启动生成中状态的定时更新
            startGeneratingStatusUpdate()
        } else {
            console.warn('API返回数据格式异常:', response)
            // 如果API返回数据异常，保持原有的默认数据
        }

    } catch (error) {
        console.error('获取我的数字人列表失败:', error)
        myDigitalHumansError.value = error.message || '获取数字人列表失败'

        // 发生错误时，可以选择保持默认数据或显示空列表
        // 这里选择保持默认数据以确保页面正常显示
    } finally {
        myDigitalHumansLoading.value = false
    }
}

// 启动生成中状态的定时更新
const startGeneratingStatusUpdate = () => {
    // 清除之前的定时器
    if (myDigitalHumansTimer.value) {
        clearInterval(myDigitalHumansTimer.value)
        myDigitalHumansTimer.value = null
    }

    // 查找生成中的数字人
    const generatingList = digitalHumansProgress.value.filter(item => item.status === 'generating')

    if (generatingList.length > 0) {
        console.log('发现生成中的数字人，启动状态更新定时器:', generatingList.length, '个')

        // 每30秒更新一次状态，与"我的作品"模块保持一致
        myDigitalHumansTimer.value = setInterval(() => {
            updateMyDigitalHumansStatus()
        }, 30000)
    }
}

// 更新我的数字人状态
const updateMyDigitalHumansStatus = async () => {
    try {
        // 获取生成中的数字人ID列表
        const generatingList = digitalHumansProgress.value.filter(item => item.status === 'generating')
        const ids = generatingList.map(item => item.id)

        if (ids.length === 0) {
            // 没有生成中的数字人，清除定时器
            if (myDigitalHumansTimer.value) {
                clearInterval(myDigitalHumansTimer.value)
                myDigitalHumansTimer.value = null
            }
            return
        }

        console.log('更新数字人状态，ID列表:', ids)

        // 调用状态更新接口
        const updatedData = await updateWorksStatus({ ids })

        if (updatedData && updatedData.length > 0) {
            // 更新本地数据
            for (let i = 0; i < updatedData.length; i++) {
                const updatedItem = updatedData[i]
                const mappedStatus = mapDigitalHumanStatus(updatedItem.status)

                // 查找并更新对应的数字人数据
                for (let j = 0; j < digitalHumansProgress.value.length; j++) {
                    if (digitalHumansProgress.value[j].id === updatedItem.id) {
                        digitalHumansProgress.value[j] = {
                            ...digitalHumansProgress.value[j],
                            status: mappedStatus,
                            progress: mappedStatus === 'generating' ? (updatedItem.progress || 0) : 0,
                            originalData: updatedItem
                        }
                        console.log(`数字人 ${updatedItem.id} 状态更新为:`, mappedStatus)
                        break
                    }
                }
            }

            // 检查是否还有生成中的数字人
            const stillGenerating = digitalHumansProgress.value.filter(item => item.status === 'generating')
            if (stillGenerating.length === 0) {
                // 没有生成中的数字人了，清除定时器
                if (myDigitalHumansTimer.value) {
                    clearInterval(myDigitalHumansTimer.value)
                    myDigitalHumansTimer.value = null
                    console.log('所有数字人生成完成，清除状态更新定时器')
                }
            }
        }
    } catch (error) {
        console.error('更新数字人状态失败:', error)
    }
}

// 获取公共数字人列表数据
const getPublicDigitalHumansList = async (page, size) => {
    try {
        const { data: { list, page_info: { total_page } } } = await getDigitalHumanList({ page: page, size: size, userId: getUserId() })
        publicDigitalTotalPage.value = total_page
        publicDigitalHumansList.value = publicDigitalHumansList.value.concat(list)
        // 过滤掉特定ID的数字人，与原页面保持一致
        publicDigitalHumansList.value = publicDigitalHumansList.value.filter(item => item.id != '5474a829b22947c69a5d0e47d3b5bee7')
        console.log('获取公共数字人列表成功:', publicDigitalHumansList.value)
    } catch (error) {
        console.error('获取公共数字人列表失败:', error)
    }
}

// 公共数字人分页数据加载
const loadPublicDigitalHumansList = () => {
    // 公共数字人分页数据加载
    if (publicDigitalCurrentPage.value >= publicDigitalTotalPage.value) {
        publicNoMoreData.value = true
    } else {
        publicNoMoreData.value = false
        publicDigitalCurrentPage.value++
        getPublicDigitalHumansList(publicDigitalCurrentPage.value, 100)
    }
}

// 获取公共数字人生成进度的接口函数（示例）
// const getPublicHumansProgress = async () => {
//     try {
//         const response = await fetch('/api/public-humans/progress')
//         const data = await response.json()
//         updatePublicHumansProgress(data)
//     } catch (error) {
//         console.error('获取公共数字人生成进度失败:', error)
//     }
// }

// 模拟进度更新的测试函数（用于演示）
const simulateProgressUpdate = () => {
	// 模拟数字人进度更新
	setInterval(() => {
		const updatedProgress = digitalHumansProgress.value.map(item => {
			if (item.status === 'generating' && item.progress < 100) {
				return { ...item, progress: Math.min(item.progress + 10, 100) }
			}
			return item
		})
		updateDigitalHumansProgress(updatedProgress)
	}, 2000) // 每2秒更新一次

	// 模拟公共数字人进度更新
	setInterval(() => {
		const updatedProgress = publicHumansProgress.value.map(item => {
			if (item.status === 'generating' && item.progress < 100) {
				return { ...item, progress: Math.min(item.progress + 15, 100) }
			}
			return item
		})
		updatePublicHumansProgress(updatedProgress)
	}, 1500) // 每1.5秒更新一次
}
// 数字人页面跳转
const jumpPage = (url)=>{
	router.push(url)
}

// 组件销毁时清理定时器
onUnmounted(() => {
	if (myDigitalHumansTimer.value) {
		clearInterval(myDigitalHumansTimer.value)
		myDigitalHumansTimer.value = null
		console.log('组件销毁，清理数字人状态更新定时器')
	}
})

</script>

<style scoped lang="scss">
// 全屏应用容器
.digital-human-transition-app {
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background-color: #FFFFFF;
}

// 主要内容区域
.main-content {
	flex: 1;
	padding: 20px 20px 20px 78px;
	margin-top: 64px; // 为Headbar组件预留空间
	overflow-y: auto;
	background-color: #FFFFFF;
}

// 顶部功能卡片区域
.feature-cards-section {
	display: flex;
	gap: 20px;
	margin-bottom: 40px;
}

.feature-card {
	cursor: pointer;
	transition: transform 0.3s ease;

	&:hover {
		transform: translateY(-2px);
	}

	img {
		width: 422px;
		height: 112px;
		object-fit: cover;
		border-radius: 8px;
	}
}

// 通用区域样式
.section-header {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 20px;

	h2 {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 28px;
		font-weight: 400;
		font-style: normal;
		line-height: 100%;
		letter-spacing: 0;
		vertical-align: middle;
		color: #333;
		margin: 0;
	}

	.view-more {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 18px;
		font-weight: 400;
		font-style: normal;
		line-height: 100%;
		letter-spacing: 0;
		vertical-align: middle;
		color: #0AAF60;
		text-decoration: none;
		margin-left: 24px;
		margin-top: 7px;
		display: flex;
		align-items: center;
		gap: 4px;

		&:hover {
			text-decoration: none;
		}

		.view-more-icon {
			width: 24px;
			height: 24px;
			object-fit: contain;
			margin-left: -6px;
			margin-top: 2px;
		}
	}
}

// 我的作品区域
.works-section {
	margin-bottom: 59px;
}

.works-container {
	// height: 400px; // 设置固定高度，避免页面过长
	overflow: hidden;
	width: 1600px;
	// max-width: calc(100vw - 78px - 40px); // 减去左边距和右边距
}

// 我的数字人区域
.digital-humans-section {
	margin-bottom: 59px;
	padding-left: 0; // 重置左边距，与我的作品对齐
}

// 加载状态样式
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200px;

	.loading-text {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 16px;
		color: #666;
	}
}

// 错误状态样式
.error-container {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 200px;
	gap: 16px;

	.error-text {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 16px;
		color: #ff4d4f;
	}

	.retry-button {
		padding: 8px 16px;
		background: #0AAF60;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 14px;
		transition: background-color 0.3s ease;

		&:hover {
			background: #099954;
		}
	}
}

// 公共数字人区域
.public-humans-section {
	padding-left: 0; // 重置左边距，与我的作品对齐
}

// 数字人网格样式
.humans-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start; // 从左往右依次排列，保持固定间距
	gap: 69px;
	width: 1535px;
}

.human-item {
	text-align: center;
	cursor: pointer;
	width: 160px; // 固定宽度，与图片宽度一致
	flex-shrink: 0; // 防止收缩
}

.human-avatar {
	position: relative;
	width: 160px;
	height: auto;
	aspect-ratio: 1/1.3;
	border-radius: 8px;
	overflow: hidden;
	background: #f0f0f0;

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}
.public-avatar{
	position: relative;
	width: 160px;
	height: auto;
	border-radius: 8px;
	overflow: hidden;
	background: #f0f0f0;
	aspect-ratio: 1/1.3;
	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.create-video-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 84px;
	height: 32px;
	background: #0AAF60;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 500;
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	cursor: pointer;

	&:hover {
		background: #099954;
	}
}

.create-video-overlay.show {
	opacity: 1;
	visibility: visible;
}

// 三个点菜单样式
.three-dots-dropdown {
	position: absolute;
	top: 8px;
	right: 8px;
	z-index: 100;
}

.three-dots {
	width: 24px;
	height: 24px;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 16px;
	font-weight: bold;
	transition: background-color 0.3s ease;
	box-shadow: none !important;
	border: none !important;
	outline: none !important;
}

// 覆盖Element Plus的默认样式
.three-dots-dropdown {
	.el-tooltip__trigger {
		box-shadow: none !important;
		border: none !important;
		outline: none !important;
	}
}



.generating-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #F1F2F4;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	border-radius: 8px;

	img {
		width: 60px;
		height: 60px;
		margin-bottom: 8px;
	}

	p {
		color: #000000A6;
		font-family: 'PingFang SC', sans-serif;
		font-weight: 500;
		font-style: normal;
		font-size: 12px;
		line-height: 21px;
		letter-spacing: 0;
		margin: 0;
		text-align: center;
	}
}

// 失败状态样式 - 与"我的作品"模块保持一致
.failed-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.4); // 半透明遮罩，与"我的作品"一致
	border-radius: 8px;

	// 右上角删除图标
	.delete-icon {
		position: absolute;
		top: 8px;
		right: 8px;
		width: 16px;
		height: 16px;
		cursor: pointer;
		z-index: 10;

		&:hover {
			opacity: 0.8;
		}
	}

	// 左下角失败标签
	.failure-label {
		position: absolute;
		bottom: 7px;
		left: 7px;
		width: 40px;
		height: 21px;
		background: #FF2D55; // 与"我的作品"一致的红色
		border-radius: 2px;
		font-size: 12px;
		color: #FFFFFF;
		text-align: center;
		line-height: 21px;
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
	}
}

.human-name {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.edit-name-container {
	display: flex;
	justify-content: center;
	height: 24px;
}

.edit-name-input {
	width: 80px;
	height: 24px;
	border: 1px solid #0AAF60;
	border-radius: 4px;
	padding: 0 8px;
	font-size: 14px;
	color: #333;
	text-align: center;
	outline: none;
	background: white;

	&:focus {
		border-color: #0AAF60;
		box-shadow: 0 0 0 2px rgba(10, 175, 96, 0.2);
	}
}

// 响应式设计
@media (max-width: 768px) {
	.main-content {
		padding: 15px;
		margin-top: 56px;
	}

	.feature-cards-section {
		flex-direction: column;
		gap: 15px;
	}

	.works-grid {
		grid-template-columns: 1fr;
	}

	.humans-grid {
		justify-content: flex-start; // 移动端也使用从左往右排列
		gap: 12px;
		width: 100%; // 移动端使用全宽
	}

	.human-avatar {
		width: 60px;
		height: 60px;
	}
}
</style>
