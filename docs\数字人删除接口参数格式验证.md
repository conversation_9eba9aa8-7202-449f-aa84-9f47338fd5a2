# 数字人删除接口参数格式验证

## 问题描述

用户反馈在调用 `batchDeleteDigitalHuman` 接口时，参数中的 `ids` 字段传递的是字符串而不是数组格式。

## API接口要求

### 正确的参数格式
- **单个删除**: `{ "ids": [3] }` （数组中包含一个ID）
- **批量删除**: `{ "ids": [3, 4, 5] }` （数组中包含多个ID）

### 错误的参数格式
- ❌ `{ "ids": "6" }` （字符串格式）
- ❌ `{ "ids": 6 }` （单个数字格式）

## 当前实现检查

### Vue组件中的调用代码
```javascript
// 删除数字人
const deleteDigitalHuman = async (index) => {
	try {
		const digitalHuman = digitalHumansProgress.value[index]
		
		// 确保ID被包装在数组中，无论ID是字符串还是数字
		const deleteParams = {
			ids: [digitalHuman.id]  // ✅ 正确：将ID包装在数组中
		}
		
		console.log('删除参数:', deleteParams)

		// 调用批量删除接口
		const response = await batchDeleteDigitalHuman(deleteParams)
		
		// 删除成功后更新UI
		digitalHumansProgress.value.splice(index, 1)

	} catch (error) {
		console.error('删除数字人失败:', error)
	}
}
```

### API函数定义
```javascript
/**
 * 批量删除数字人作品
 * @param {Object} params - 请求参数对象
 * @param {number[]} params.ids - 数字人ID数组，支持单个或多个ID
 */
export const batchDeleteDigitalHuman = (params) => post('/material/digital/batchDeleteDigitalHuman', params)
```

## 验证结果

### ✅ 代码实现正确
当前的Vue组件实现已经正确地将ID包装在数组中：
- 使用 `ids: [digitalHuman.id]` 确保即使是单个ID也是数组格式
- 添加了详细的调试日志来验证参数格式

### 🔍 可能的问题原因
如果仍然出现字符串格式的问题，可能的原因包括：

1. **数据类型问题**: 数字人ID本身可能是字符串类型，但这不影响数组包装
2. **网络请求序列化**: 在HTTP请求过程中参数被错误序列化
3. **缓存问题**: 浏览器可能使用了旧版本的代码

## 调试建议

### 1. 添加更详细的日志
```javascript
console.log('数字人对象:', digitalHuman)
console.log('数字人ID:', digitalHuman.id, '类型:', typeof digitalHuman.id)
console.log('删除参数:', deleteParams)
console.log('参数中的ids:', deleteParams.ids, '是否为数组:', Array.isArray(deleteParams.ids))
```

### 2. 验证数组格式
```javascript
// 确保参数格式正确
const deleteParams = {
	ids: Array.isArray([digitalHuman.id]) ? [digitalHuman.id] : [digitalHuman.id]
}
```

### 3. 检查网络请求
在浏览器开发者工具的Network标签中查看实际发送的请求体，确认参数格式。

## 解决方案

当前代码实现是正确的，如果仍然出现问题，建议：

1. 清除浏览器缓存并重新加载页面
2. 检查浏览器开发者工具中的Network请求，确认实际发送的参数格式
3. 如果问题持续存在，可能需要检查后端接口的参数解析逻辑

## 实现日期

2025-01-27

## 状态

✅ 代码实现正确，等待进一步验证
